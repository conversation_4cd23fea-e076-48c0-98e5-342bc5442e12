/**
 * SECURE Multi-Provider AI Orchestration Service
 * Implements secure backend proxy for AI services with HIPAA compliance
 *
 * SECURITY FEATURES:
 * - Server-side API key management
 * - Session-based authentication
 * - Comprehensive audit logging
 * - Rate limiting with emergency bypass
 */

class AIOrchestrator {
  constructor() {
    this.baseUrl = `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'}/api/ai-chat`;
    this.providers = {
      openai: { name: 'OpenAI', priority: 1, available: true, failures: 0 },
      anthropic: { name: 'Anthropic', priority: 2, available: true, failures: 0 },
      cohere: { name: 'Cohere', priority: 3, available: true, failures: 0 }
    };

    this.fallbackChain = ['openai', 'anthropic', 'cohere'];
    this.costTracker = { total: 0, byProvider: {} };
    this.conversationMemory = new Map();
  }

  /**
   * Generate response using secure backend proxy
   */
  async generateResponse(options = {}) {
    try {
      console.log('🤖 Starting secure AI response generation...', {
        agentType: options.agentType,
        messageCount: options.messages?.length
      });

      // Get authentication token
      const authToken = await this.getAuthToken();
      if (!authToken) {
        throw new Error('Authentication required for AI chat service');
      }

      // Validate required parameters
      if (!options.sessionId) {
        throw new Error('Session ID is required for secure AI chat');
      }

      if (!options.messages || !Array.isArray(options.messages)) {
        throw new Error('Messages array is required');
      }

      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`,
          ...(options.emergencyOverride && { 'X-Emergency-Override': 'true' })
        },
        body: JSON.stringify({
          messages: options.messages,
          sessionId: options.sessionId,
          agentType: options.agentType || 'general-practitioner',
          maxTokens: options.maxTokens || 500,
          temperature: options.temperature || 0.7
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('❌ Secure AI chat error:', response.status, errorData);

        // Handle specific error codes
        if (response.status === 401) {
          throw new Error('Authentication failed. Please log in again.');
        } else if (response.status === 403) {
          throw new Error('Access denied to this session.');
        } else if (response.status === 429) {
          throw new Error('Rate limit exceeded. Please try again later.');
        }

        throw new Error(`AI chat service error: ${errorData.error || 'Unknown error'}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'AI response generation failed');
      }

      console.log('✅ Secure AI response generated successfully');

      return {
        content: result.data.content,
        agentType: result.data.agentType,
        usage: result.data.usage,
        processingTime: result.data.processing_time
      };

    } catch (error) {
      console.error('❌ Secure AI orchestration error:', error);

      // Enhanced error handling for secure service
      if (error.message.includes('Authentication')) {
        // Clear potentially invalid auth token
        this.clearAuthToken();
      }

      throw error;
    }
  }

  /**
   * Process medical conversation with intelligent routing (legacy method)
   */
  async processConversation(sessionId, messages, options = {}) {
    // Redirect to secure generateResponse method
    return this.generateResponse({
      sessionId,
      messages,
      ...options
    });
  }

  /**
   * Get authentication token from current session
   */
  async getAuthToken() {
    try {
      // Get token from Supabase auth context
      const { supabase } = await import('../utils/supabaseClient');
      const { data: { session } } = await supabase.auth.getSession();
      return session?.access_token;
    } catch (error) {
      console.error('Failed to get auth token:', error);
      return null;
    }
  }

  /**
   * Clear authentication token (for error recovery)
   */
  async clearAuthToken() {
    try {
      const { supabase } = await import('../utils/supabaseClient');
      await supabase.auth.signOut();
    } catch (error) {
      console.error('Failed to clear auth token:', error);
    }
  }

  /**
   * Assess conversation complexity for optimal routing
   */
  assessComplexity(messages) {
    let score = 0;
    const lastMessage = messages[messages.length - 1]?.content || '';
    const text = lastMessage.toLowerCase();
    
    // Medical terminology
    const medicalTerms = [
      'symptoms', 'diagnosis', 'treatment', 'medication', 'prescription',
      'disease', 'infection', 'chronic', 'acute', 'syndrome', 'clinical'
    ];
    
    medicalTerms.forEach(term => {
      if (text.includes(term)) score += 10;
    });
    
    // Emergency indicators
    const emergencyTerms = [
      'emergency', 'urgent', 'severe', 'critical', 'chest pain',
      'difficulty breathing', 'unconscious', 'bleeding'
    ];
    
    emergencyTerms.forEach(term => {
      if (text.includes(term)) score += 25;
    });
    
    // Length and conversation history
    score += Math.min(30, text.length / 50);
    score += Math.min(20, messages.length * 2);
    
    if (score > 80) return 'high';
    if (score > 40) return 'medium';
    return 'low';
  }

  /**
   * Select optimal provider based on complexity and health
   */
  selectProvider(complexity, options = {}) {
    const { forceProvider, priority = 'balanced' } = options;
    
    if (forceProvider && this.isProviderHealthy(forceProvider)) {
      return forceProvider;
    }
    
    const healthyProviders = this.fallbackChain.filter(p => this.isProviderHealthy(p));
    
    if (healthyProviders.length === 0) {
      throw new Error('No healthy AI providers available');
    }
    
    // Route based on complexity
    switch (complexity) {
      case 'high':
        return healthyProviders.includes('openai') ? 'openai' : healthyProviders[0];
      case 'medium':
        if (priority === 'cost') {
          return healthyProviders.includes('cohere') ? 'cohere' : healthyProviders[0];
        }
        return healthyProviders.includes('anthropic') ? 'anthropic' : healthyProviders[0];
      case 'low':
        return healthyProviders.includes('cohere') ? 'cohere' : healthyProviders[0];
      default:
        return healthyProviders[0];
    }
  }

  /**
   * Execute request with fallback chain
   */
  async executeWithFallback(primaryProvider, request) {
    const providers = [primaryProvider, ...this.fallbackChain.filter(p => p !== primaryProvider)];
    
    for (const provider of providers) {
      if (!this.isProviderHealthy(provider)) continue;
      
      try {
        const result = await this.callProvider(provider, request);
        return result;
      } catch (error) {
        this.recordFailure(provider, error);
        console.warn(`Provider ${provider} failed:`, error.message);
        
        if (provider === providers[providers.length - 1]) {
          throw new Error('All AI providers failed');
        }
      }
    }
  }

  /**
   * Call specific AI provider
   */
  async callProvider(provider, request) {
    const model = this.selectModel(provider, request.complexity);
    
    switch (provider) {
      case 'openai':
        return await this.callOpenAI(model, request);
      case 'anthropic':
        return await this.callAnthropic(model, request);
      case 'cohere':
        return await this.callCohere(model, request);
      default:
        throw new Error(`Unknown provider: ${provider}`);
    }
  }

  /**
   * Call OpenAI API
   */
  async callOpenAI(model, request) {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${import.meta.env.VITE_OPENAI_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: model || 'gpt-4',
        messages: request.messages,
        temperature: 0.7,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      content: data.choices[0].message.content,
      usage: data.usage,
      confidence: 0.9
    };
  }

  /**
   * Call Anthropic API
   */
  async callAnthropic(model, request) {
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'x-api-key': import.meta.env.VITE_ANTHROPIC_API_KEY,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: model || 'claude-3-sonnet-20240229',
        max_tokens: 1000,
        messages: request.messages
      })
    });

    if (!response.ok) {
      throw new Error(`Anthropic API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      content: data.content[0].text,
      usage: data.usage,
      confidence: 0.85
    };
  }

  /**
   * Call Cohere API
   */
  async callCohere(model, request) {
    const lastMessage = request.messages[request.messages.length - 1];
    const response = await fetch('https://api.cohere.ai/v1/generate', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${import.meta.env.VITE_COHERE_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: model || 'command',
        prompt: lastMessage.content,
        max_tokens: 1000,
        temperature: 0.7
      })
    });

    if (!response.ok) {
      throw new Error(`Cohere API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      content: data.generations[0].text,
      usage: { total_tokens: data.meta?.billed_units?.output_tokens || 0 },
      confidence: 0.8
    };
  }

  /**
   * Select appropriate model for provider
   */
  selectModel(provider, complexity) {
    const models = {
      openai: { high: 'gpt-4', medium: 'gpt-3.5-turbo', low: 'gpt-3.5-turbo' },
      anthropic: { high: 'claude-3-opus-20240229', medium: 'claude-3-sonnet-20240229', low: 'claude-3-sonnet-20240229' },
      cohere: { high: 'command', medium: 'command', low: 'command' }
    };
    
    return models[provider]?.[complexity] || models[provider]?.medium;
  }

  /**
   * Check if provider is healthy
   */
  isProviderHealthy(provider) {
    const providerData = this.providers[provider];
    return providerData?.available && providerData.failures < 5;
  }

  /**
   * Record successful request
   */
  recordSuccess(provider) {
    if (this.providers[provider]) {
      this.providers[provider].failures = 0;
    }
  }

  /**
   * Record failed request
   */
  recordFailure(provider, error) {
    if (this.providers[provider]) {
      this.providers[provider].failures += 1;
      console.warn(`Provider ${provider} failure count: ${this.providers[provider].failures}`);
    }
  }

  /**
   * Update conversation memory for context
   */
  updateConversationMemory(sessionId, messages, response) {
    const existing = this.conversationMemory.get(sessionId) || [];
    const updated = [...existing, ...messages, { role: 'assistant', content: response.content }];
    
    // Keep only last 10 messages to manage memory
    this.conversationMemory.set(sessionId, updated.slice(-10));
  }

  /**
   * Get provider status for monitoring
   */
  getProviderStatus() {
    return Object.entries(this.providers).map(([key, provider]) => ({
      provider: key,
      name: provider.name,
      available: provider.available,
      healthy: this.isProviderHealthy(key),
      failures: provider.failures
    }));
  }

  /**
   * Initialize the orchestrator
   */
  async initialize() {
    console.log('🚀 Initializing AI Orchestrator...');
    // Perform any initialization logic here
    return Promise.resolve();
  }

  /**
   * Generate AI response using the orchestrator
   */
  async generateResponse(request) {
    const { messages, agentType, complexity = 'medium', options = {} } = request;
    
    // Use processConversation for consistency
    const sessionId = options.sessionId || `session_${Date.now()}`;
    
    return await this.processConversation(sessionId, messages, {
      agentType,
      complexity,
      ...options
    });
  }

  /**
   * Get available providers
   */
  getAvailableProviders() {
    return Object.keys(this.providers);
  }

  /**
   * Check health of all providers
   */
  async checkProvidersHealth() {
    const healthStatus = {};
    
    for (const provider of Object.keys(this.providers)) {
      healthStatus[provider] = this.isProviderHealthy(provider) ? 'healthy' : 'unhealthy';
    }
    
    return healthStatus;
  }

  /**
   * Get provider statistics
   */
  getProviderStats() {
    return {
      providers: this.providers,
      costTracker: this.costTracker,
      memorySize: this.conversationMemory.size
    };
  }
}

export default new AIOrchestrator();
