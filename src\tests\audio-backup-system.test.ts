/**
 * COMPREHENSIVE AUDIO BACKUP SYSTEM TESTS
 * 
 * Tests for automated backup strategies including:
 * - Local and cloud backup creation
 * - Backup integrity verification
 * - Data recovery mechanisms
 * - Retention policy enforcement
 * - Emergency backup access
 * - Cross-region replication
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import audioBackupService from '../utils/audioBackupService';
import type {
  AudioMessage,
  BackupResult,
  DataRecoveryResult,
  BackupVerificationResult
} from '../types/audio';

// Mock implementations
const mockSupabase = {
  storage: {
    from: vi.fn().mockReturnValue({
      upload: vi.fn(),
      download: vi.fn(),
      remove: vi.fn(),
      getPublicUrl: vi.fn()
    })
  },
  from: vi.fn().mockReturnValue({
    insert: vi.fn(),
    select: vi.fn().mockReturnValue({
      eq: vi.fn().mockReturnValue({
        single: vi.fn(),
        order: vi.fn().mockReturnValue({
          eq: vi.fn()
        })
      }),
      lt: vi.fn()
    }),
    delete: vi.fn().mockReturnValue({
      eq: vi.fn()
    })
  })
};

// Mock IndexedDB
const mockIndexedDB = {
  open: vi.fn(),
  databases: vi.fn()
};

// Mock crypto
const mockCrypto = {
  subtle: {
    digest: vi.fn()
  }
};

// Create mock audio message
const createMockAudioMessage = (overrides: Partial<AudioMessage> = {}): AudioMessage => ({
  id: 'msg-123',
  sessionId: '12345678-1234-1234-1234-123456789012',
  userId: 'user-123',
  speakerId: 'speaker-123',
  speakerName: 'Test User',
  messageType: 'user_voice',
  encryptedAudioData: {
    encrypted: true,
    algorithm: 'AES-256-GCM',
    keyLength: 256,
    iv: 'mock-iv',
    authTag: 'mock-auth-tag',
    encryptedData: 'mock-encrypted-data',
    timestamp: new Date().toISOString()
  },
  duration: 5.0,
  quality: 'medium',
  transcription: 'Test transcription',
  confidence: 0.95,
  timestamp: new Date().toISOString(),
  status: 'synced',
  size: 1024,
  encrypted: true,
  originalChecksum: 'abc123',
  encryptedChecksum: 'def456',
  checksumAlgorithm: 'SHA-256',
  metadata: {
    sampleRate: 22050,
    bitRate: 64000,
    format: 'webm',
    codec: 'opus',
    encryption: {
      algorithm: 'AES-256-GCM',
      keyLength: 256,
      encrypted: true,
      timestamp: new Date().toISOString()
    },
    integrity: {
      originalChecksum: 'abc123',
      encryptedChecksum: 'def456',
      algorithm: 'SHA-256',
      verified: false
    }
  },
  ...overrides
});

describe('Audio Backup System Tests', () => {
  beforeEach(() => {
    // Setup global mocks
    global.indexedDB = mockIndexedDB as any;
    global.crypto = mockCrypto as any;

    // Reset all mocks
    vi.clearAllMocks();

    // Setup default mock responses
    mockCrypto.subtle.digest.mockResolvedValue(new ArrayBuffer(32));
    
    mockSupabase.storage.from().upload.mockResolvedValue({
      data: { path: 'mock-path' },
      error: null
    });

    mockSupabase.from().insert.mockResolvedValue({ error: null });
    mockSupabase.from().select().eq().single.mockResolvedValue({
      data: null,
      error: null
    });

    // Mock IndexedDB operations
    const mockDB = {
      transaction: vi.fn().mockReturnValue({
        objectStore: vi.fn().mockReturnValue({
          add: vi.fn().mockReturnValue({
            onsuccess: null,
            onerror: null
          }),
          get: vi.fn().mockReturnValue({
            onsuccess: null,
            onerror: null
          }),
          getAll: vi.fn().mockReturnValue({
            onsuccess: null,
            onerror: null
          }),
          delete: vi.fn().mockReturnValue({
            onsuccess: null,
            onerror: null
          })
        })
      })
    };

    mockIndexedDB.open.mockReturnValue({
      onsuccess: null,
      onerror: null,
      onupgradeneeded: null,
      result: mockDB
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Backup Creation', () => {
    it('should create full backup with all message data', async () => {
      const audioMessage = createMockAudioMessage();
      
      // Mock successful backup creation
      vi.spyOn(audioBackupService as any, 'storeLocalBackup').mockResolvedValue(true);
      vi.spyOn(audioBackupService as any, 'storeCloudBackup').mockResolvedValue(true);
      vi.spyOn(audioBackupService as any, 'replicateToSecondaryRegion').mockResolvedValue(true);

      const result = await audioBackupService.createBackup(audioMessage, 'full');

      expect(result.success).toBe(true);
      expect(result.backupId).toBeDefined();
      expect(result.location).toBe('both');
      expect(result.size).toBeGreaterThan(0);
      expect(result.checksum).toBeDefined();
    });

    it('should create incremental backup with essential data only', async () => {
      const audioMessage = createMockAudioMessage();
      
      vi.spyOn(audioBackupService as any, 'storeLocalBackup').mockResolvedValue(true);
      vi.spyOn(audioBackupService as any, 'storeCloudBackup').mockResolvedValue(false);

      const result = await audioBackupService.createBackup(audioMessage, 'incremental');

      expect(result.success).toBe(true);
      expect(result.location).toBe('local');
    });

    it('should handle backup creation failures gracefully', async () => {
      const audioMessage = createMockAudioMessage();
      
      vi.spyOn(audioBackupService as any, 'storeLocalBackup').mockResolvedValue(false);
      vi.spyOn(audioBackupService as any, 'storeCloudBackup').mockResolvedValue(false);

      const result = await audioBackupService.createBackup(audioMessage, 'full');

      expect(result.success).toBe(false);
    });

    it('should apply correct retention policies based on consultation type', async () => {
      const emergencyMessage = createMockAudioMessage({
        transcription: 'Emergency medical situation requiring immediate attention'
      });

      vi.spyOn(audioBackupService as any, 'storeLocalBackup').mockResolvedValue(true);
      vi.spyOn(audioBackupService as any, 'storeCloudBackup').mockResolvedValue(true);

      const result = await audioBackupService.createBackup(emergencyMessage, 'full');

      expect(result.success).toBe(true);
      // Emergency consultations should have longer retention (7 years)
    });

    it('should generate unique backup IDs and checksums', async () => {
      const audioMessage = createMockAudioMessage();
      
      vi.spyOn(audioBackupService as any, 'storeLocalBackup').mockResolvedValue(true);
      vi.spyOn(audioBackupService as any, 'storeCloudBackup').mockResolvedValue(true);

      const result1 = await audioBackupService.createBackup(audioMessage, 'full');
      const result2 = await audioBackupService.createBackup(audioMessage, 'full');

      expect(result1.backupId).not.toBe(result2.backupId);
      expect(result1.checksum).toBeDefined();
      expect(result2.checksum).toBeDefined();
    });
  });

  describe('Backup Integrity Verification', () => {
    it('should verify backup integrity with matching checksums', async () => {
      const backupId = 'backup-123';
      
      // Mock backup metadata and data
      vi.spyOn(audioBackupService as any, 'getBackupMetadata').mockResolvedValue({
        backupId,
        messageId: 'msg-123',
        checksum: 'valid-checksum',
        size: 1024,
        encrypted: true,
        location: 'local'
      });

      vi.spyOn(audioBackupService as any, 'getBackupData').mockResolvedValue({
        messageId: 'msg-123',
        backupType: 'full',
        data: 'mock-backup-data'
      });

      vi.spyOn(audioBackupService as any, 'generateBackupChecksum').mockResolvedValue('valid-checksum');

      const result = await audioBackupService.verifyBackupIntegrity(backupId);

      expect(result.valid).toBe(true);
      expect(result.backupId).toBe(backupId);
      expect(result.integrityChecks.checksumMatch).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect corrupted backups with mismatched checksums', async () => {
      const backupId = 'backup-corrupted';
      
      vi.spyOn(audioBackupService as any, 'getBackupMetadata').mockResolvedValue({
        backupId,
        messageId: 'msg-123',
        checksum: 'original-checksum',
        size: 1024,
        encrypted: true,
        location: 'local'
      });

      vi.spyOn(audioBackupService as any, 'getBackupData').mockResolvedValue({
        messageId: 'msg-123',
        data: 'corrupted-data'
      });

      vi.spyOn(audioBackupService as any, 'generateBackupChecksum').mockResolvedValue('different-checksum');

      const result = await audioBackupService.verifyBackupIntegrity(backupId);

      expect(result.valid).toBe(false);
      expect(result.integrityChecks.checksumMatch).toBe(false);
      expect(result.errors).toContain(
        expect.stringContaining('checksum verification failed')
      );
    });

    it('should handle missing backup metadata gracefully', async () => {
      const backupId = 'backup-missing';
      
      vi.spyOn(audioBackupService as any, 'getBackupMetadata').mockResolvedValue(null);

      const result = await audioBackupService.verifyBackupIntegrity(backupId);

      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Backup metadata not found');
    });

    it('should validate encryption requirements', async () => {
      const backupId = 'backup-encryption-test';
      
      vi.spyOn(audioBackupService as any, 'getBackupMetadata').mockResolvedValue({
        backupId,
        encrypted: true,
        checksum: 'test-checksum',
        size: 1024,
        location: 'local'
      });

      vi.spyOn(audioBackupService as any, 'getBackupData').mockResolvedValue({
        encryptedAudioData: {
          encrypted: true,
          algorithm: 'AES-256-GCM'
        }
      });

      vi.spyOn(audioBackupService as any, 'generateBackupChecksum').mockResolvedValue('test-checksum');

      const result = await audioBackupService.verifyBackupIntegrity(backupId);

      expect(result.integrityChecks.encryptionValid).toBe(true);
    });
  });

  describe('Data Recovery', () => {
    it('should recover data from local backup successfully', async () => {
      const messageId = 'msg-recovery-test';
      
      // Mock available backups
      vi.spyOn(audioBackupService as any, 'findBackupsForMessage').mockResolvedValue([
        {
          backupId: 'backup-local',
          messageId,
          location: 'local',
          backupType: 'full',
          timestamp: new Date().toISOString()
        }
      ]);

      // Mock successful verification
      vi.spyOn(audioBackupService, 'verifyBackupIntegrity').mockResolvedValue({
        valid: true,
        backupId: 'backup-local',
        integrityChecks: {
          checksumMatch: true,
          sizeMatch: true,
          encryptionValid: true,
          metadataValid: true
        },
        errors: [],
        warnings: []
      });

      // Mock backup data retrieval
      vi.spyOn(audioBackupService as any, 'getBackupData').mockResolvedValue({
        fullMessage: createMockAudioMessage({ id: messageId })
      });

      const result = await audioBackupService.recoverFromBackup(messageId, 'local');

      expect(result.success).toBe(true);
      expect(result.source).toBe('local_backup');
      expect(result.data.id).toBe(messageId);
      expect(result.integrityVerified).toBe(true);
      expect(result.recoveryTime).toBeGreaterThan(0);
    });

    it('should fallback to cloud backup when local backup fails', async () => {
      const messageId = 'msg-fallback-test';
      
      vi.spyOn(audioBackupService as any, 'findBackupsForMessage').mockResolvedValue([
        {
          backupId: 'backup-local-corrupted',
          messageId,
          location: 'local',
          backupType: 'full',
          timestamp: new Date().toISOString()
        },
        {
          backupId: 'backup-cloud-good',
          messageId,
          location: 'cloud',
          backupType: 'full',
          timestamp: new Date().toISOString()
        }
      ]);

      // Mock local backup verification failure
      vi.spyOn(audioBackupService, 'verifyBackupIntegrity')
        .mockResolvedValueOnce({
          valid: false,
          backupId: 'backup-local-corrupted',
          integrityChecks: {
            checksumMatch: false,
            sizeMatch: true,
            encryptionValid: true,
            metadataValid: true
          },
          errors: ['Checksum mismatch'],
          warnings: []
        })
        .mockResolvedValueOnce({
          valid: true,
          backupId: 'backup-cloud-good',
          integrityChecks: {
            checksumMatch: true,
            sizeMatch: true,
            encryptionValid: true,
            metadataValid: true
          },
          errors: [],
          warnings: []
        });

      vi.spyOn(audioBackupService as any, 'getBackupData')
        .mockResolvedValueOnce(null) // Local backup data retrieval fails
        .mockResolvedValueOnce({
          fullMessage: createMockAudioMessage({ id: messageId })
        });

      const result = await audioBackupService.recoverFromBackup(messageId, 'auto');

      expect(result.success).toBe(true);
      expect(result.source).toBe('cloud_backup');
    });

    it('should handle recovery failure when no valid backups exist', async () => {
      const messageId = 'msg-no-backups';
      
      vi.spyOn(audioBackupService as any, 'findBackupsForMessage').mockResolvedValue([]);

      const result = await audioBackupService.recoverFromBackup(messageId);

      expect(result.success).toBe(false);
      expect(result.recoveryTime).toBeGreaterThan(0);
    });

    it('should prioritize full backups over incremental backups', async () => {
      const messageId = 'msg-priority-test';
      
      vi.spyOn(audioBackupService as any, 'findBackupsForMessage').mockResolvedValue([
        {
          backupId: 'backup-incremental',
          messageId,
          location: 'local',
          backupType: 'incremental',
          timestamp: new Date(Date.now() - 1000).toISOString() // Newer
        },
        {
          backupId: 'backup-full',
          messageId,
          location: 'local',
          backupType: 'full',
          timestamp: new Date(Date.now() - 2000).toISOString() // Older
        }
      ]);

      vi.spyOn(audioBackupService, 'verifyBackupIntegrity').mockResolvedValue({
        valid: true,
        backupId: 'backup-full',
        integrityChecks: {
          checksumMatch: true,
          sizeMatch: true,
          encryptionValid: true,
          metadataValid: true
        },
        errors: [],
        warnings: []
      });

      vi.spyOn(audioBackupService as any, 'getBackupData').mockResolvedValue({
        fullMessage: createMockAudioMessage({ id: messageId })
      });

      const result = await audioBackupService.recoverFromBackup(messageId);

      expect(result.success).toBe(true);
      // Should have attempted full backup first despite being older
    });
  });

  describe('Retention Policy Management', () => {
    it('should apply correct retention periods for different consultation types', () => {
      const retentionPolicies = audioBackupService.getRetentionPolicies();

      const emergencyPolicy = retentionPolicies.find(p => p.consultationType === 'emergency');
      const generalPolicy = retentionPolicies.find(p => p.consultationType === 'general');
      const mentalHealthPolicy = retentionPolicies.find(p => p.consultationType === 'mental_health');
      const pediatricPolicy = retentionPolicies.find(p => p.consultationType === 'pediatric');

      expect(emergencyPolicy?.retentionDays).toBe(2555); // 7 years
      expect(generalPolicy?.retentionDays).toBe(1825); // 5 years
      expect(mentalHealthPolicy?.retentionDays).toBe(2555); // 7 years
      expect(pediatricPolicy?.retentionDays).toBe(6570); // 18 years

      // All should require secure deletion and audit logging
      expect(emergencyPolicy?.secureDeleteRequired).toBe(true);
      expect(emergencyPolicy?.auditLogRequired).toBe(true);
    });

    it('should clean up expired backups according to retention policies', async () => {
      // Mock expired backups
      vi.spyOn(audioBackupService as any, 'cleanupLocalBackups').mockResolvedValue(5);
      vi.spyOn(audioBackupService as any, 'cleanupCloudBackups').mockResolvedValue(3);

      const result = await audioBackupService.cleanupExpiredBackups();

      expect(result.deleted).toBe(8);
      expect(result.errors).toHaveLength(0);
    });

    it('should handle cleanup errors gracefully', async () => {
      vi.spyOn(audioBackupService as any, 'cleanupLocalBackups').mockRejectedValue(new Error('Local cleanup failed'));
      vi.spyOn(audioBackupService as any, 'cleanupCloudBackups').mockResolvedValue(2);

      const result = await audioBackupService.cleanupExpiredBackups();

      expect(result.deleted).toBe(2);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]).toContain('Local cleanup error');
    });
  });

  describe('Backup Configuration and Monitoring', () => {
    it('should provide backup configuration details', () => {
      const config = audioBackupService.getBackupConfig();

      expect(config.localRetentionDays).toBe(30);
      expect(config.cloudRetentionDays).toBe(365);
      expect(config.encryptionRequired).toBe(true);
      expect(config.crossRegionReplication).toBe(true);
      expect(config.maxBackupSize).toBe(100 * 1024 * 1024); // 100MB
    });

    it('should track backup schedule status', () => {
      const schedule = audioBackupService.getBackupSchedule();

      expect(schedule).toHaveProperty('nextIncremental');
      expect(schedule).toHaveProperty('nextFull');
      expect(schedule).toHaveProperty('lastBackup');
      expect(schedule).toHaveProperty('backupCount');
      expect(typeof schedule.backupCount).toBe('number');
    });

    it('should prevent concurrent backup operations', async () => {
      const audioMessage = createMockAudioMessage();
      
      // Mock backup in progress
      (audioBackupService as any).backupInProgress = true;

      await expect(audioBackupService.createBackup(audioMessage)).rejects.toThrow(
        'Backup operation already in progress'
      );
    });
  });

  describe('Emergency Backup Access', () => {
    it('should provide emergency access to backups', async () => {
      const messageId = 'emergency-msg';
      
      // Mock emergency backup scenario
      vi.spyOn(audioBackupService as any, 'findBackupsForMessage').mockResolvedValue([
        {
          backupId: 'emergency-backup',
          messageId,
          location: 'cloud',
          backupType: 'full',
          timestamp: new Date().toISOString()
        }
      ]);

      vi.spyOn(audioBackupService, 'verifyBackupIntegrity').mockResolvedValue({
        valid: true,
        backupId: 'emergency-backup',
        integrityChecks: {
          checksumMatch: true,
          sizeMatch: true,
          encryptionValid: true,
          metadataValid: true
        },
        errors: [],
        warnings: []
      });

      vi.spyOn(audioBackupService as any, 'getBackupData').mockResolvedValue({
        fullMessage: createMockAudioMessage({ 
          id: messageId,
          transcription: 'Emergency medical situation'
        })
      });

      const result = await audioBackupService.recoverFromBackup(messageId, 'cloud');

      expect(result.success).toBe(true);
      expect(result.data.transcription).toContain('Emergency');
    });

    it('should maintain emergency response time requirements', async () => {
      const messageId = 'emergency-response-test';
      const startTime = performance.now();
      
      // Mock quick emergency backup access
      vi.spyOn(audioBackupService as any, 'findBackupsForMessage').mockResolvedValue([
        {
          backupId: 'emergency-backup',
          messageId,
          location: 'local',
          backupType: 'full',
          timestamp: new Date().toISOString()
        }
      ]);

      vi.spyOn(audioBackupService, 'verifyBackupIntegrity').mockResolvedValue({
        valid: true,
        backupId: 'emergency-backup',
        integrityChecks: {
          checksumMatch: true,
          sizeMatch: true,
          encryptionValid: true,
          metadataValid: true
        },
        errors: [],
        warnings: []
      });

      vi.spyOn(audioBackupService as any, 'getBackupData').mockResolvedValue({
        fullMessage: createMockAudioMessage({ id: messageId })
      });

      const result = await audioBackupService.recoverFromBackup(messageId, 'local');
      const responseTime = performance.now() - startTime;

      expect(result.success).toBe(true);
      expect(responseTime).toBeLessThan(2000); // Emergency response within 2 seconds
    });
  });
});
