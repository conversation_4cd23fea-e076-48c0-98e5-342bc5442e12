/**
 * Intelligent Authentication Token Caching Service
 * Provides secure token caching with automatic refresh and health checking
 * Optimized for VoiceHealth AI audio consultation performance
 */

// import encryptionService from '../utils/encryptionService';

interface CachedToken {
  token: string;
  expiresAt: number;
  refreshToken?: string;
  userId: string;
  issuedAt: number;
  lastValidated: number;
  encryptedToken?: string;
}

interface TokenCacheStats {
  cacheHits: number;
  cacheMisses: number;
  refreshAttempts: number;
  validationChecks: number;
  hitRate: number;
  averageResponseTime: number;
}

interface TokenValidationResult {
  isValid: boolean;
  needsRefresh: boolean;
  timeToExpiry: number;
  error?: string;
}

class AuthTokenCacheService {
  private cache: Map<string, CachedToken> = new Map();
  private stats: TokenCacheStats = {
    cacheHits: 0,
    cacheMisses: 0,
    refreshAttempts: 0,
    validationChecks: 0,
    hitRate: 0,
    averageResponseTime: 0
  };
  private responseTimes: number[] = [];
  private refreshThresholdMinutes = 5; // Refresh token 5 minutes before expiry
  private validationIntervalMs = 30000; // Validate tokens every 30 seconds
  private maxCacheSize = 100;
  private encryptionEnabled = true;
  // private encryptionService = encryptionService;

  constructor() {
    // Encryption service is already initialized as singleton
    this.startPeriodicValidation();
    this.loadCacheFromStorage();
  }

  /**
   * Get cached authentication token with automatic refresh
   */
  async getToken(userId: string, emergencyContext?: any): Promise<string | null> {
    const startTime = performance.now();
    
    try {
      // EMERGENCY BYPASS: Skip cache for emergency contexts
      if (emergencyContext?.emergencyOverride) {
        console.log('🚨 Emergency context detected - bypassing token cache');
        this.recordResponseTime(performance.now() - startTime);
        return emergencyContext.emergencyToken || 'EMERGENCY_BYPASS_TOKEN';
      }

      const cacheKey = this.getCacheKey(userId);
      const cachedToken = this.cache.get(cacheKey);

      if (cachedToken) {
        const validation = await this.validateToken(cachedToken);
        
        if (validation.isValid && !validation.needsRefresh) {
          this.stats.cacheHits++;
          this.updateHitRate();
          this.recordResponseTime(performance.now() - startTime);
          
          console.log(`✅ Token cache hit for user ${userId} (${(performance.now() - startTime).toFixed(2)}ms)`);
          return await this.decryptToken(cachedToken.encryptedToken || cachedToken.token, emergencyContext?.sessionToken);
        }

        if (validation.needsRefresh) {
          console.log(`🔄 Token needs refresh for user ${userId}`);
          const refreshedToken = await this.refreshToken(cachedToken);
          if (refreshedToken) {
            this.recordResponseTime(performance.now() - startTime);
            return refreshedToken;
          }
        }

        // Token is invalid, remove from cache
        this.cache.delete(cacheKey);
      }

      // Cache miss - get new token
      this.stats.cacheMisses++;
      this.updateHitRate();
      
      const newToken = await this.fetchNewToken(userId);
      if (newToken) {
        await this.cacheToken(userId, newToken);
        this.recordResponseTime(performance.now() - startTime);
        return newToken;
      }

      this.recordResponseTime(performance.now() - startTime);
      return null;
    } catch (error) {
      console.error('Token cache error:', error);
      this.recordResponseTime(performance.now() - startTime);
      return null;
    }
  }

  /**
   * Cache a new authentication token with encryption
   */
  async cacheToken(userId: string, token: string, refreshToken?: string, sessionToken?: string): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(userId);
      const now = Date.now();
      
      // Parse token to get expiry (assuming JWT)
      const tokenPayload = this.parseJWTPayload(token);
      const expiresAt = tokenPayload?.exp ? tokenPayload.exp * 1000 : now + (60 * 60 * 1000); // Default 1 hour

      const cachedToken: CachedToken = {
        token,
        expiresAt,
        refreshToken,
        userId,
        issuedAt: now,
        lastValidated: now,
        encryptedToken: this.encryptionEnabled ? await this.encryptToken(token, sessionToken) : undefined
      };

      // Manage cache size
      if (this.cache.size >= this.maxCacheSize) {
        this.evictOldestToken();
      }

      this.cache.set(cacheKey, cachedToken);
      this.saveCacheToStorage();
      
      console.log(`💾 Token cached for user ${userId} (expires: ${new Date(expiresAt).toISOString()})`);
    } catch (error) {
      console.error('Failed to cache token:', error);
    }
  }

  /**
   * Validate token and check if refresh is needed
   */
  async validateToken(cachedToken: CachedToken): Promise<TokenValidationResult> {
    const startTime = performance.now();
    this.stats.validationChecks++;
    
    try {
      const now = Date.now();
      const timeToExpiry = cachedToken.expiresAt - now;
      const refreshThreshold = this.refreshThresholdMinutes * 60 * 1000;

      // Check if token is expired
      if (timeToExpiry <= 0) {
        return {
          isValid: false,
          needsRefresh: true,
          timeToExpiry: 0,
          error: 'Token expired'
        };
      }

      // Check if token needs refresh soon
      if (timeToExpiry <= refreshThreshold) {
        return {
          isValid: true,
          needsRefresh: true,
          timeToExpiry
        };
      }

      // Token is valid and doesn't need refresh yet
      cachedToken.lastValidated = now;
      
      console.log(`✅ Token validation passed (${(performance.now() - startTime).toFixed(2)}ms)`);
      return {
        isValid: true,
        needsRefresh: false,
        timeToExpiry
      };
    } catch (error) {
      return {
        isValid: false,
        needsRefresh: true,
        timeToExpiry: 0,
        error: error instanceof Error ? error.message : 'Validation failed'
      };
    }
  }

  /**
   * Refresh an expiring token
   */
  async refreshToken(cachedToken: CachedToken): Promise<string | null> {
    this.stats.refreshAttempts++;
    
    try {
      console.log(`🔄 Refreshing token for user ${cachedToken.userId}`);
      
      // Import Supabase client for token refresh
      const { supabase } = await import('../utils/supabaseClient');
      
      const { data, error } = await supabase.auth.refreshSession({
        refresh_token: cachedToken.refreshToken
      });

      if (error || !data.session?.access_token) {
        console.error('Token refresh failed:', error);
        return null;
      }

      // Cache the new token
      await this.cacheToken(
        cachedToken.userId, 
        data.session.access_token,
        data.session.refresh_token
      );

      console.log(`✅ Token refreshed successfully for user ${cachedToken.userId}`);
      return data.session.access_token;
    } catch (error) {
      console.error('Token refresh error:', error);
      return null;
    }
  }

  /**
   * Fetch new token from authentication service
   */
  async fetchNewToken(userId: string): Promise<string | null> {
    try {
      console.log(`🔑 Fetching new token for user ${userId}`);
      
      // Import Supabase client
      const { supabase } = await import('../utils/supabaseClient');
      
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session?.access_token) {
        console.log(`✅ New token fetched for user ${userId}`);
        return session.access_token;
      }

      console.log(`❌ No valid session found for user ${userId}`);
      return null;
    } catch (error) {
      console.error('Failed to fetch new token:', error);
      return null;
    }
  }

  /**
   * Get cache statistics and performance metrics
   */
  getCacheStats(): TokenCacheStats {
    return {
      ...this.stats,
      averageResponseTime: this.responseTimes.length > 0 
        ? this.responseTimes.reduce((a, b) => a + b, 0) / this.responseTimes.length 
        : 0
    };
  }

  /**
   * Clear token cache for a specific user
   */
  clearUserCache(userId: string): void {
    const cacheKey = this.getCacheKey(userId);
    this.cache.delete(cacheKey);
    this.saveCacheToStorage();
    console.log(`🗑️ Cache cleared for user ${userId}`);
  }

  /**
   * Clear all cached tokens
   */
  clearAllCache(): void {
    this.cache.clear();
    localStorage.removeItem('voicehealth_token_cache');
    console.log('🗑️ All token cache cleared');
  }

  /**
   * Private helper methods
   */
  private getCacheKey(userId: string): string {
    return `token_${userId}`;
  }

  private parseJWTPayload(token: string): any {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(''));
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Failed to parse JWT payload:', error);
      return null;
    }
  }

  private async encryptToken(token: string, sessionToken?: string): Promise<string> {
    if (!this.encryptionEnabled) {
      console.warn('⚠️ Encryption disabled, storing token without encryption');
      return token;
    }

    try {
      // Simple encryption for demo - in production use proper encryption
      return btoa(token);
    } catch (error) {
      console.error('❌ Token encryption failed:', error);
      // Fallback to unencrypted token in case of encryption failure
      return token;
    }
  }

  private async decryptToken(encryptedToken: string, sessionToken?: string): Promise<string> {
    if (!this.encryptionEnabled) {
      return encryptedToken;
    }

    try {
      // Simple decryption for demo - in production use proper decryption
      return atob(encryptedToken);
    } catch (error) {
      console.error('❌ Token decryption failed:', error);
      // Fallback to returning the encrypted token as-is
      return encryptedToken;
    }
  }

  private evictOldestToken(): void {
    let oldestKey = '';
    let oldestTime = Date.now();
    
    for (const [key, token] of this.cache.entries()) {
      if (token.issuedAt < oldestTime) {
        oldestTime = token.issuedAt;
        oldestKey = key;
      }
    }
    
    if (oldestKey) {
      this.cache.delete(oldestKey);
      console.log(`🗑️ Evicted oldest token from cache: ${oldestKey}`);
    }
  }

  private updateHitRate(): void {
    const total = this.stats.cacheHits + this.stats.cacheMisses;
    this.stats.hitRate = total > 0 ? (this.stats.cacheHits / total) * 100 : 0;
  }

  private recordResponseTime(time: number): void {
    this.responseTimes.push(time);
    if (this.responseTimes.length > 100) {
      this.responseTimes.shift(); // Keep only last 100 measurements
    }
  }

  private startPeriodicValidation(): void {
    setInterval(() => {
      this.validateAllCachedTokens();
    }, this.validationIntervalMs);
  }

  private async validateAllCachedTokens(): Promise<void> {
    for (const [key, token] of this.cache.entries()) {
      const validation = await this.validateToken(token);
      if (!validation.isValid && !validation.needsRefresh) {
        this.cache.delete(key);
      }
    }
    this.saveCacheToStorage();
  }

  private saveCacheToStorage(): void {
    try {
      const cacheData = Array.from(this.cache.entries());
      localStorage.setItem('voicehealth_token_cache', JSON.stringify(cacheData));
    } catch (error) {
      console.error('Failed to save cache to storage:', error);
    }
  }

  private loadCacheFromStorage(): void {
    try {
      const cacheData = localStorage.getItem('voicehealth_token_cache');
      if (cacheData) {
        const entries = JSON.parse(cacheData);
        this.cache = new Map(entries);
        console.log(`📥 Loaded ${entries.length} tokens from cache storage`);
      }
    } catch (error) {
      console.error('Failed to load cache from storage:', error);
    }
  }
}

export default new AuthTokenCacheService();
