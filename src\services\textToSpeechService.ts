/**
 * SECURE TEXT-TO-SPEECH SERVICE (TypeScript)
 * 
 * Handles speech synthesis for voice consultations via secure server-side proxy
 * with comprehensive type safety, encryption support, and emergency protocols.
 * 
 * SECURITY FEATURES:
 * - No client-side API keys
 * - Server-side authentication and authorization
 * - HIPAA-compliant request handling
 * - Comprehensive audit logging
 * - Type-safe voice management
 */

import type {
  AudioBlob,
  TextToSpeechOptions,
  TextToSpeechResponse,
  ServiceResponse,
  HealthStatus,
  AudioError,
  EmergencyStopEvent
} from '../types/audio';

interface TextToSpeechConfig {
  readonly baseUrl: string;
  readonly timeout: number;
  readonly maxRetries: number;
  readonly emergencyResponseTime: number; // Must be < 2000ms
  readonly maxTextLength: number;
}

interface SynthesisResult {
  readonly audioBlob: AudioBlob;
  readonly audioUrl: string;
  readonly duration: number;
  readonly voiceId: string;
  readonly processingTime: number;
}

interface VoiceProfile {
  readonly id: string;
  readonly name: string;
  readonly agentType: string;
  readonly language: string;
  readonly gender: 'male' | 'female' | 'neutral';
  readonly description: string;
  readonly stability: number;
  readonly similarity_boost: number;
}

interface AgentVoiceMapping {
  readonly [agentType: string]: string;
}

class TextToSpeechService {
  private readonly config: TextToSpeechConfig;
  private readonly agentVoices: AgentVoiceMapping;
  private readonly defaultVoiceId: string;
  private readonly fallbackVoiceId: string;
  private authTokenCache: string | null = null;
  private lastHealthCheck: Date | null = null;
  private healthStatus: HealthStatus | null = null;

  constructor() {
    this.config = {
      baseUrl: `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'}/api/text-to-speech`,
      timeout: 30000, // 30 seconds
      maxRetries: 3,
      emergencyResponseTime: 2000, // 2 seconds for emergency stop
      maxTextLength: 5000 // 5000 characters maximum
    };

    this.defaultVoiceId = 'ErXwobaYiN019PkySvjV'; // Antoni voice (clear, professional)
    this.fallbackVoiceId = 'EXAVITQu4vr4xnSDxMaL'; // Sarah voice (warm, caring)
    
    // Voice configurations for different agent types
    this.agentVoices = {
      'general-practitioner': 'ErXwobaYiN019PkySvjV', // Antoni - professional
      'cardiologist': 'VR6AewLTigWG4xSOukaG', // Arnold - authoritative
      'pediatrician': 'EXAVITQu4vr4xnSDxMaL', // Sarah - warm
      'mental-health': 'pNInz6obpgDQGcFmaJgB', // Adam - calm
      'emergency': 'VR6AewLTigWG4xSOukaG', // Arnold - clear and urgent
      'default': 'ErXwobaYiN019PkySvjV'
    } as const;
  }

  /**
   * Convert text to speech using secure backend proxy
   */
  async synthesizeSpeech(
    text: string, 
    options: TextToSpeechOptions
  ): Promise<ServiceResponse<SynthesisResult>> {
    const startTime = Date.now();
    
    try {
      console.log('🔊 Starting secure text-to-speech synthesis...', { 
        textLength: text.length,
        voice: options.voiceId || 'default',
        sessionId: options.sessionId
      });

      // Validate inputs
      this.validateSynthesisInputs(text, options);

      // Get authentication token with emergency bypass support
      const authToken = await this.getAuthToken(options);
      if (!authToken && !options.emergencyOverride) {
        throw new AudioError('Authentication required for text-to-speech service', {
          code: 'AUTH_REQUIRED',
          severity: 'high',
          recoverable: true
        });
      }

      // For emergency override, use emergency token or bypass token
      const finalAuthToken = authToken || (options.emergencyOverride ? 'EMERGENCY_BYPASS_TOKEN' : null);
      if (!finalAuthToken) {
        throw new AudioError('Authentication required for text-to-speech service', {
          code: 'AUTH_REQUIRED',
          severity: 'high',
          recoverable: true
        });
      }

      // Prepare request body
      const voiceId = this.getVoiceForAgent(options.agentType) || options.voiceId || this.defaultVoiceId;
      const requestBody = {
        text,
        sessionId: options.sessionId,
        voiceId,
        stability: this.clampValue(options.stability || 0.5, 0, 1),
        similarity_boost: this.clampValue(options.similarity_boost || 0.75, 0, 1)
      };

      // Make secure API request with emergency bypass support
      const response = await this.makeSecureRequest(requestBody, finalAuthToken, options);
      
      if (!response.ok) {
        return this.handleErrorResponse(response, startTime);
      }

      const result = await response.json() as TextToSpeechResponse;
      
      if (!result.success || !result.data) {
        throw new AudioError(result.error || 'Text-to-speech synthesis failed', {
          code: result.code || 'SYNTHESIS_FAILED',
          severity: 'medium',
          recoverable: true
        });
      }

      // Convert base64 audio data to blob
      const audioBlob = this.base64ToAudioBlob(result.data.audioData, result.data.audioFormat);
      const audioUrl = URL.createObjectURL(audioBlob);

      const processingTime = Date.now() - startTime;
      console.log('✅ Secure speech synthesis successful:', { 
        size: audioBlob.size,
        duration: result.data.duration,
        processingTime: result.data.processingTime
      });

      return {
        success: true,
        data: {
          audioBlob,
          audioUrl,
          duration: result.data.duration,
          voiceId: result.data.voiceId,
          processingTime: result.data.processingTime
        },
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      return this.handleSynthesisError(error as Error, startTime, options);
    }
  }

  /**
   * Validate synthesis inputs
   */
  private validateSynthesisInputs(
    text: string, 
    options: TextToSpeechOptions
  ): void {
    if (!text || text.trim().length === 0) {
      throw new AudioError('Text is required for speech synthesis', {
        code: 'MISSING_TEXT',
        severity: 'medium',
        recoverable: false
      });
    }

    if (text.length > this.config.maxTextLength) {
      throw new AudioError(`Text too long: ${text.length} characters. Maximum allowed: ${this.config.maxTextLength}`, {
        code: 'TEXT_TOO_LONG',
        severity: 'medium',
        recoverable: false
      });
    }

    if (!options.sessionId) {
      throw new AudioError('Session ID is required for secure text-to-speech', {
        code: 'MISSING_SESSION_ID',
        severity: 'high',
        recoverable: false
      });
    }

    if (!options.sessionToken) {
      throw new AudioError('Session token is required for encryption', {
        code: 'MISSING_SESSION_TOKEN',
        severity: 'high',
        recoverable: false
      });
    }

    // Validate session ID format
    if (!/^[a-f0-9-]{36}$/.test(options.sessionId)) {
      throw new AudioError('Invalid session ID format', {
        code: 'INVALID_SESSION_FORMAT',
        severity: 'medium',
        recoverable: false
      });
    }
  }

  /**
   * Make secure API request with proper headers
   */
  private async makeSecureRequest(
    requestBody: Record<string, unknown>,
    authToken: string,
    options: TextToSpeechOptions
  ): Promise<Response> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${authToken}`
    };

    if (options.emergencyOverride) {
      headers['X-Emergency-Override'] = 'true';
    }

    return fetch(this.config.baseUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(this.config.timeout)
    });
  }

  /**
   * Handle error responses from the API
   */
  private async handleErrorResponse(
    response: Response,
    startTime: number
  ): Promise<ServiceResponse<SynthesisResult>> {
    const processingTime = Date.now() - startTime;
    
    try {
      const errorData = await response.json() as TextToSpeechResponse;
      console.error('❌ Secure text-to-speech error:', response.status, errorData);
      
      let errorMessage: string;
      let errorCode: string;
      
      switch (response.status) {
        case 401:
          errorMessage = 'Authentication failed. Please log in again.';
          errorCode = 'AUTH_FAILED';
          await this.clearAuthToken();
          break;
        case 403:
          errorMessage = 'Access denied to this session.';
          errorCode = 'ACCESS_DENIED';
          break;
        case 429:
          errorMessage = 'Rate limit exceeded. Please try again later.';
          errorCode = 'RATE_LIMITED';
          break;
        default:
          errorMessage = errorData.error || 'Text-to-speech service error';
          errorCode = errorData.code || 'SERVICE_ERROR';
      }
      
      return {
        success: false,
        error: errorMessage,
        code: errorCode,
        timestamp: new Date().toISOString()
      };
    } catch (parseError) {
      return {
        success: false,
        error: `Service error: ${response.status}`,
        code: 'PARSE_ERROR',
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Handle synthesis errors with proper typing
   */
  private async handleSynthesisError(
    error: Error,
    startTime: number,
    options: TextToSpeechOptions
  ): Promise<ServiceResponse<SynthesisResult>> {
    const processingTime = Date.now() - startTime;
    
    console.error('❌ Secure text-to-speech error:', error);
    
    // Enhanced error handling for secure service
    if (error.message.includes('Authentication')) {
      await this.clearAuthToken();
    }

    // Check if this is an emergency situation requiring immediate response
    if (processingTime > this.config.emergencyResponseTime && options.emergencyOverride) {
      await this.triggerEmergencyProtocol(error, options);
    }
    
    const audioError = error as AudioError;
    
    return {
      success: false,
      error: audioError.message || 'Text-to-speech synthesis failed',
      code: audioError.code || 'UNKNOWN_ERROR',
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Convert base64 audio data to AudioBlob
   */
  private base64ToAudioBlob(audioData: string, audioFormat: string): AudioBlob {
    try {
      const audioBytes = Uint8Array.from(atob(audioData), c => c.charCodeAt(0));
      return new Blob([audioBytes], { type: audioFormat }) as AudioBlob;
    } catch (error) {
      throw new AudioError('Failed to convert audio data', {
        code: 'AUDIO_CONVERSION_FAILED',
        severity: 'medium',
        recoverable: false
      });
    }
  }

  /**
   * Get voice ID for specific agent type
   */
  private getVoiceForAgent(agentType?: string): string | undefined {
    if (!agentType) return undefined;
    return this.agentVoices[agentType] || this.agentVoices.default;
  }

  /**
   * Clamp value between min and max
   */
  private clampValue(value: number, min: number, max: number): number {
    return Math.max(min, Math.min(max, value));
  }

  /**
   * Get authentication token from current session with emergency bypass support
   */
  private async getAuthToken(options?: TextToSpeechOptions): Promise<string | null> {
    const startTime = performance.now();

    try {
      // EMERGENCY BYPASS: Check for emergency override first
      if (options?.emergencyOverride) {
        const { default: emergencyAuthService } = await import('./emergencyAuthService');

        const emergencyAuthContext = {
          emergencyOverride: true,
          emergencyToken: options.emergencyToken,
          reason: options.emergencyReason || 'text_to_speech_emergency',
          sessionId: options.sessionId,
          bypassAuthentication: true
        };

        const isEmergencyValid = await emergencyAuthService.validateEmergencyAuth(emergencyAuthContext);

        if (isEmergencyValid) {
          const responseTime = performance.now() - startTime;
          console.log(`🚨 Emergency auth bypass activated for text-to-speech in ${responseTime.toFixed(2)}ms`);

          // Return emergency token for immediate access
          return options.emergencyToken || 'EMERGENCY_BYPASS_TOKEN';
        }
      }

      // Return cached token if still valid and not in emergency mode
      if (this.authTokenCache && !options?.emergencyOverride) {
        const responseTime = performance.now() - startTime;
        console.log(`✅ Using cached auth token in ${responseTime.toFixed(2)}ms`);
        return this.authTokenCache;
      }

      // Get token from Supabase auth context (normal flow)
      const { supabase } = await import('../utils/supabaseClient');
      const { data: { session } } = await supabase.auth.getSession();

      if (session?.access_token) {
        this.authTokenCache = session.access_token;
        const responseTime = performance.now() - startTime;
        console.log(`✅ Retrieved new auth token in ${responseTime.toFixed(2)}ms`);
        return session.access_token;
      }

      const responseTime = performance.now() - startTime;
      console.log(`❌ No auth token available in ${responseTime.toFixed(2)}ms`);
      return null;
    } catch (error) {
      const responseTime = performance.now() - startTime;
      console.error(`❌ Failed to get auth token in ${responseTime.toFixed(2)}ms:`, error);
      return null;
    }
  }

  /**
   * Clear authentication token (for error recovery)
   */
  private async clearAuthToken(): Promise<void> {
    try {
      this.authTokenCache = null;
      const { supabase } = await import('../utils/supabaseClient');
      await supabase.auth.signOut();
    } catch (error) {
      console.error('Failed to clear auth token:', error);
    }
  }

  /**
   * Trigger emergency protocol for critical synthesis failures
   */
  private async triggerEmergencyProtocol(
    error: Error,
    options: TextToSpeechOptions
  ): Promise<void> {
    try {
      const emergencyEvent: EmergencyStopEvent = {
        triggered: true,
        reason: 'synthesis_critical_failure',
        timestamp: new Date().toISOString(),
        responseTime: Date.now(),
        userId: 'system',
        sessionId: options.sessionId
      };

      // Log emergency event
      const { default: auditLogger } = await import('../utils/auditLogger');
      await auditLogger.logEmergencyAccess(
        'system',
        'system',
        `Critical synthesis failure: ${error.message}`,
        {
          session_id: options.sessionId,
          error_message: error.message,
          emergency_protocols_triggered: true,
          timestamp: emergencyEvent.timestamp
        }
      );

      console.log('🚨 Emergency protocols triggered for synthesis failure');
    } catch (emergencyError) {
      console.error('Failed to trigger emergency protocol:', emergencyError);
    }
  }

  /**
   * Get available voice profiles
   */
  getAvailableVoices(): readonly VoiceProfile[] {
    return [
      {
        id: 'ErXwobaYiN019PkySvjV',
        name: 'Antoni',
        agentType: 'general-practitioner',
        language: 'en',
        gender: 'male',
        description: 'Clear, professional voice for general medical consultations',
        stability: 0.5,
        similarity_boost: 0.75
      },
      {
        id: 'VR6AewLTigWG4xSOukaG',
        name: 'Arnold',
        agentType: 'cardiologist',
        language: 'en',
        gender: 'male',
        description: 'Authoritative voice for specialist consultations',
        stability: 0.6,
        similarity_boost: 0.8
      },
      {
        id: 'EXAVITQu4vr4xnSDxMaL',
        name: 'Sarah',
        agentType: 'pediatrician',
        language: 'en',
        gender: 'female',
        description: 'Warm, caring voice for pediatric consultations',
        stability: 0.4,
        similarity_boost: 0.7
      },
      {
        id: 'pNInz6obpgDQGcFmaJgB',
        name: 'Adam',
        agentType: 'mental-health',
        language: 'en',
        gender: 'male',
        description: 'Calm, reassuring voice for mental health consultations',
        stability: 0.3,
        similarity_boost: 0.6
      }
    ] as const;
  }

  /**
   * Estimate audio duration from text length
   */
  estimateDuration(text: string): number {
    // Rough estimate: ~150 words per minute, ~5 characters per word
    const wordsPerMinute = 150;
    const charactersPerWord = 5;
    const estimatedWords = text.length / charactersPerWord;
    return Math.ceil((estimatedWords / wordsPerMinute) * 60); // Duration in seconds
  }

  /**
   * Check if service is configured properly
   */
  isConfigured(): boolean {
    return Boolean(this.config.baseUrl);
  }

  /**
   * Get service health status
   */
  async getHealthStatus(): Promise<HealthStatus> {
    try {
      // Return cached status if recent
      if (this.healthStatus && this.lastHealthCheck) {
        const timeSinceCheck = Date.now() - this.lastHealthCheck.getTime();
        if (timeSinceCheck < 60000) { // 1 minute cache
          return this.healthStatus;
        }
      }

      const authToken = await this.getAuthToken();
      if (!authToken) {
        return {
          healthy: false,
          services: { authentication: false, textToSpeech: false },
          lastCheck: new Date().toISOString(),
          error: 'Authentication required'
        };
      }

      const response = await fetch(`${this.config.baseUrl.replace('/text-to-speech', '/health')}`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        },
        signal: AbortSignal.timeout(5000) // 5 second timeout for health check
      });

      const healthy = response.ok;
      let data: unknown = null;
      
      if (healthy) {
        data = await response.json();
      }

      this.healthStatus = {
        healthy,
        services: { 
          authentication: true, 
          textToSpeech: healthy 
        },
        lastCheck: new Date().toISOString(),
        ...(data && { data })
      };
      
      this.lastHealthCheck = new Date();
      return this.healthStatus;

    } catch (error) {
      const healthStatus: HealthStatus = {
        healthy: false,
        services: { authentication: false, textToSpeech: false },
        lastCheck: new Date().toISOString(),
        error: (error as Error).message
      };
      
      this.healthStatus = healthStatus;
      this.lastHealthCheck = new Date();
      return healthStatus;
    }
  }
}

// Custom error class for audio-specific errors
class AudioError extends Error {
  public readonly code?: string;
  public readonly severity?: 'low' | 'medium' | 'high' | 'critical';
  public readonly recoverable?: boolean;
  public readonly audioContext?: Record<string, unknown>;

  constructor(
    message: string, 
    options?: {
      code?: string;
      severity?: 'low' | 'medium' | 'high' | 'critical';
      recoverable?: boolean;
      audioContext?: Record<string, unknown>;
    }
  ) {
    super(message);
    this.name = 'AudioError';
    this.code = options?.code;
    this.severity = options?.severity;
    this.recoverable = options?.recoverable;
    this.audioContext = options?.audioContext;
  }
}

// Export singleton instance
export default new TextToSpeechService();
